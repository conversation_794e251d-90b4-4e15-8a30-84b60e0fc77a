from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from typing import Optional, List, Dict
from datetime import datetime
import uuid
from urllib.parse import urlparse
import logging

from utils.auth import get_current_user, get_db
from database.models import (
    User,
    Generation,
    GenerationStatus,
    Style,
    GenerationImage,
    SourceImage,
    TemplateType,
    CoinTransaction,
    CoinTransactionSource,
)
from services.generation_service import GenerationService, StyleFactory
from utils.content_security import is_image_safe, check_image_security  # 导入内容安全检测函数
from config import Config

router = APIRouter(
    prefix="/wowpic/generate",
    tags=["图片生成"],
)

logger = logging.getLogger(__name__)


def process_variable_prompt(style, variables: dict) -> str:
    """
    处理变量提示词，生成最终的提示词文本

    Args:
        style: 风格对象
        variables: 变量字典

    Returns:
        str: 处理后的提示词文本

    Raises:
        HTTPException: 当变量缺失或内容为空时
    """
    if not variables:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="VARIABLE_PROMPT 风格必须提交 variables 字段")

    try:
        # 特殊处理：对于自由创作风格（prompt_template为空），直接使用detail变量的值
        if not style.prompt_template or style.prompt_template.strip() == '':
            # 自由创作风格，直接使用detail变量作为提示词
            detail_value = variables.get('detail', '')
            if not detail_value.strip():
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="自由创作风格必须提供描述内容")
            return detail_value
        else:
            # 普通变量提示词风格，使用模板格式化
            return style.prompt_template.format(**variables)
    except KeyError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"缺少变量: {e.args[0]}")

# 工具函数：确保路径以 / 开头
def _ensure_path_format(path: str) -> str:
    """确保路径格式正确，返回相对路径"""
    if not path:
        return path
    if path.startswith("http"):
        # 如果是完整URL，尝试提取相对路径部分
        parsed = urlparse(path)
        path = parsed.path
    # 确保 path 以 '/' 开头
    if not path.startswith("/"):
        path = "/" + path
    return path

# 新增：处理图片安全检测
async def _check_images_security(image_urls: List[str], user_id: int = None, style_id: int = None, task_id: str = None) -> List[str]:
    """
    检查生成的图片是否安全，并过滤掉不安全的图片

    Args:
        image_urls: 图片URL列表
        user_id: 用户ID（用于日志）
        style_id: 风格ID（用于日志）
        task_id: 任务ID（用于日志）

    Returns:
        List[str]: 安全图片的URL列表
    """
    safe_urls = []
    success_count = 0

    for url in image_urls:
        try:
            check_result = await check_image_security(url)

            # 当前策略：总是允许图片显示，同时记录检测结果
            if check_result["success"]:
                success_count += 1
            else:
                # 只有失败时才打印详细错误日志
                logger.error(f"❌ 内容安全检测提交失败: 用户ID{user_id} - 风格ID{style_id} - 任务ID{task_id} - "
                           f"图片URL: {url} - 错误: {check_result.get('message', '未知错误')}")

            # 添加到安全URL列表
            safe_urls.append(url)
        except Exception as e:
            # 异常时打印完整错误日志
            logger.error(f"❌ 内容安全检测异常: 用户ID{user_id} - 风格ID{style_id} - 任务ID{task_id} - "
                        f"图片URL: {url} - 异常: {str(e)}")
            # 发生异常时，默认允许图片显示
            safe_urls.append(url)

    # 成功时只打印一条汇总日志
    if success_count > 0:
        logger.info(f"用户ID{user_id} - 风格ID{style_id} - 任务ID{task_id} - 内容安全检测通过")

    return safe_urls

### 通用自由创作路由放在顶部，避免被 /{style_id} 捕获 ###

@router.post("/generic")
async def generate_generic_image(
    prompt: str = Body(...),
    source_image_urls: List[str] = Body(default=[]),
    ratio: Optional[str] = Body(default="1:1"),  # 修改默认值为"1:1"
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """通用自由创作（同步），无需指定风格ID"""
    # 打印请求日志，方便调试
    logger.info(f"[Generic] 用户{current_user.id} 请求同步生成, prompt_len={len(prompt)}, imgs={len(source_image_urls)}, ratio={ratio}")

    # 查找通用风格 (model_identifier = generic_v1)
    style = db.query(Style).filter(Style.model_identifier == "generic_v1").first()
    if not style:
        raise HTTPException(status_code=500, detail="通用风格未初始化")

    # 校验余额
    if current_user.coins < style.cost:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail="哇图币不足")

    # 扣费
    current_user.coins -= style.cost

    # 将ratio参数包含在prompt中，确保传递给风格实现
    # 检查prompt中是否已包含ratio参数，避免重复添加
    has_ratio = '"ratio":' in prompt or 'ratio' in prompt.lower()
    final_prompt = prompt
    if not has_ratio and ratio:
        final_prompt = f"{prompt}\n\"ratio\": \"{ratio}\""

    # 构造 Generation
    generation = Generation(
        task_id=str(uuid.uuid4()),
        user_id=current_user.id,
        style_id=style.id,
        prompt=final_prompt,  # 使用处理后的提示词
        images_count=1,
        status=GenerationStatus.PENDING,
        cost=style.cost,
        created_at=datetime.utcnow()
    )
    db.add(generation)
    db.commit()
    db.refresh(generation)

    # 扣费流水
    db.add(
        CoinTransaction(
            user_id=current_user.id,
            change=-style.cost,
            balance=current_user.coins,
            source=CoinTransactionSource.GENERATE,
            source_id=generation.id,
            remark="通用生成扣费",
        )
    )
    db.commit()

    # 保存源图
    for url in source_image_urls:
        db.add(SourceImage(generation_id=generation.id, image_url=url))
    db.commit()

    # 调用生成
    service = GenerationService(db)
    try:
        image_urls = await service.generate_image(generation)
        if not image_urls:
            raise RuntimeError("未能生成任何图片")

        generation.status = GenerationStatus.SUCCESS
        generation.images_count = len(image_urls)
        generation.completed_at = datetime.utcnow()

        # 内容安全检测
        safe_urls = await _check_images_security(image_urls, current_user.id, style.id, generation.task_id)
        for url in safe_urls:
            db.add(GenerationImage(generation_id=generation.id, image_url=url))
        db.commit()

        return {"generated_image_urls": [_ensure_path_format(u) for u in safe_urls]}
    except Exception as e:
        # 记录异常堆栈
        logger.exception(f"[Generic] 同步生成异常: {e}")
        generation.status = GenerationStatus.FAILED
        generation.error_message = str(e)
        generation.completed_at = datetime.utcnow()
        db.commit()
        current_user.coins += style.cost
        db.add(
            CoinTransaction(
                user_id=current_user.id,
                change=style.cost,
                balance=current_user.coins,
                source=CoinTransactionSource.REFUND,
                source_id=generation.id,
                remark="通用生成失败返还",
            )
        )
        db.commit()
        raise HTTPException(status_code=500, detail=f"生成失败: {e}")


@router.post("/generic/async")
async def generate_generic_image_async(
    prompt: str = Body(...),
    source_image_urls: List[str] = Body(default=[]),
    ratio: Optional[str] = Body(default="1:1"),  # 修改默认值为"1:1"
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """通用自由创作（异步）"""
    logger.info(f"[Generic-Async] 用户{current_user.id} 请求异步生成, prompt_len={len(prompt)}, imgs={len(source_image_urls)}, ratio={ratio}")

    style = db.query(Style).filter(Style.model_identifier == "generic_v1").first()
    if not style:
        raise HTTPException(status_code=500, detail="通用风格未初始化")

    if current_user.coins < style.cost:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail="哇图币不足")

    current_user.coins -= style.cost

    # 将ratio参数包含在prompt中，确保传递给风格实现
    # 检查prompt中是否已包含ratio参数，避免重复添加
    has_ratio = '"ratio":' in prompt or 'ratio' in prompt.lower()
    final_prompt = prompt
    if not has_ratio and ratio:
        final_prompt = f"{prompt}\n\"ratio\": \"{ratio}\""

    generation = Generation(
        task_id=str(uuid.uuid4()),
        user_id=current_user.id,
        style_id=style.id,
        prompt=final_prompt,
        images_count=1,
        status=GenerationStatus.PENDING,
        cost=style.cost,
        created_at=datetime.utcnow()
    )
    db.add(generation)
    db.commit()
    db.refresh(generation)

    db.add(
        CoinTransaction(
            user_id=current_user.id,
            change=-style.cost,
            balance=current_user.coins,
            source=CoinTransactionSource.GENERATE,
            source_id=generation.id,
            remark="通用生成扣费",
        )
    )
    db.commit()

    for url in source_image_urls:
        db.add(SourceImage(generation_id=generation.id, image_url=url))
    db.commit()

    service = GenerationService(db)
    service.generate_image_background(generation)

    return {"task_id": generation.task_id, "generation_id": generation.id, "status": "PENDING"}

# --------------- 以上为新增位置 ----------------

@router.post("/{style_id}")
async def generate_image(
    style_id: int,
    prompt: Optional[str] = Body(None),
    variables: Dict[str, str] = Body(default={}),
    source_image_urls: List[str] = Body(default=[]),
    n: Optional[int] = Body(None),  # n可选，仅部分风格使用
    ratio: Optional[str] = Body(default="1:1"),  # 添加ratio参数
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """创建图片生成请求并立即处理（同步实现）"""

    # 校验风格
    style = db.query(Style).filter(Style.id == style_id).first()
    if not style:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="风格不存在")

    # 判断该风格是否支持 n
    supports_n = style.model_identifier in {"cat_travel_v1", "pet_id_photo_v1"}

    # 处理 n 值
    if supports_n:
        n_val = n if n is not None else 2  # 默认 2
        if n_val < 1 or n_val > 4:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="n参数必须在1-4之间")
    else:
        n_val = 1  # 其他风格固定 1

    # 校验余额
    if current_user.coins < style.cost:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail="哇图币不足")

    # 扣除费用并暂不写流水，稍后在生成记录完成后补充 source_id
    current_user.coins -= style.cost

    # 生成最终提示词
    if style.template_type == TemplateType.VARIABLE_PROMPT:
        if prompt is not None:
            # 为防泄露模板，禁止直接传完整 prompt
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="VARIABLE_PROMPT 风格请仅提交 variables，不允许直接提交 prompt")
        base_prompt_text = process_variable_prompt(style, variables)
    else:
        base_prompt_text = prompt if prompt is not None else style.prompt_template

    # 添加ratio信息到提示词中
    # 检查prompt中是否已包含ratio参数，避免重复添加
    has_ratio = '"ratio":' in base_prompt_text or 'ratio' in base_prompt_text.lower()
    if not has_ratio and ratio:
        base_prompt_text = f"{base_prompt_text}\n\"ratio\": \"{ratio}\""

    if supports_n:
        final_prompt_text_sync = f"{base_prompt_text}\n\"n\": {n_val}"
    else:
        final_prompt_text_sync = base_prompt_text
    generation = Generation(
        task_id=str(uuid.uuid4()),
        user_id=current_user.id,
        style_id=style_id,
        prompt=final_prompt_text_sync,
        images_count=n_val,
        status=GenerationStatus.PENDING,
        cost=style.cost,
        created_at=datetime.utcnow()
    )
    db.add(generation)
    db.commit()
    db.refresh(generation)

    # 写入扣费流水
    deduct_tx = CoinTransaction(
        user_id=current_user.id,
        change=-style.cost,
        balance=current_user.coins,
        source=CoinTransactionSource.GENERATE,
        source_id=generation.id,
        remark="生成图片扣费",
    )
    db.add(deduct_tx)
    db.commit()

    # 保存源图片列表
    for url in source_image_urls:
        db.add(SourceImage(generation_id=generation.id, image_url=url))
    db.commit()

    # 调用异步生成
    service = GenerationService(db)
    try:
        image_urls = await service.generate_image(generation)
        
        # 根据生成结果更新状态
        if not image_urls:
            # 没有生成任何图片，标记为失败
            generation.status = GenerationStatus.FAILED
            generation.error_message = "未能生成任何图片"
            generation.images_count = 0
            db.commit()
            
            # 返还哇图币
            current_user.coins += style.cost
            # 写入返还流水
            db.add(
                CoinTransaction(
                    user_id=current_user.id,
                    change=style.cost,
                    balance=current_user.coins,
                    source=CoinTransactionSource.REFUND,
                    source_id=generation.id,
                    remark="生成失败返还",
                )
            )
            db.commit()
            
            raise HTTPException(status_code=500, detail="生成失败: 未能生成任何图片")
        elif len(image_urls) < n_val and supports_n:
            # 部分成功
            generation.status = GenerationStatus.PARTIAL
        else:
            # 完全成功
            generation.status = GenerationStatus.SUCCESS
        
        # 对生成的图片进行内容安全检测
        safe_image_urls = await _check_images_security(image_urls, current_user.id, style_id, generation.task_id)
        
        # 保存所有成功生成的图片URL
        for url in safe_image_urls:
            db.add(GenerationImage(
                generation_id=generation.id,
                image_url=url
            ))
        
        generation.images_count = len(safe_image_urls)
        generation.completed_at = datetime.utcnow()
        db.commit()
        
        # 格式化返回URLs
        formatted_urls = [_ensure_path_format(url) for url in safe_image_urls]
        
        return {
            "generation_id": generation.id,
            "generated_image_urls": formatted_urls,
            "status": str(generation.status.value),
            "images_count": len(safe_image_urls),
            "expected_count": n_val
        }
    except Exception as e:
        # 发生错误时，更新状态为失败
        generation.status = GenerationStatus.FAILED
        generation.error_message = str(e)
        generation.images_count = 0
        generation.completed_at = datetime.utcnow()
        
        # 返还哇图币
        current_user.coins += style.cost
        db.add(
            CoinTransaction(
                user_id=current_user.id,
                change=style.cost,
                balance=current_user.coins,
                source=CoinTransactionSource.REFUND,
                source_id=generation.id,
                remark="生成异常返还",
            )
        )
        db.commit()
        
        raise HTTPException(status_code=500, detail=f"生成失败: {e}")

@router.post("/{style_id}/async")
async def generate_image_async(
    style_id: int,
    prompt: Optional[str] = Body(None),
    variables: Dict[str, str] = Body(default={}),
    source_image_urls: List[str] = Body(default=[]),
    n: Optional[int] = Body(None),  # n可选，仅部分风格使用
    ratio: Optional[str] = Body(default="1:1"),  # 添加ratio参数
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """创建图片生成请求（异步实现）- 立即返回任务ID，不等待生成完成"""

    # 校验风格
    style = db.query(Style).filter(Style.id == style_id).first()
    if not style:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="风格不存在")

    # 判断该风格是否支持 n
    supports_n = style.model_identifier in {"cat_travel_v1", "pet_id_photo_v1"}

    # 处理 n 值
    if supports_n:
        n_val = n if n is not None else 2
        if n_val < 1 or n_val > 4:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="n参数必须在1-4之间")
    else:
        n_val = 1

    # 校验余额
    if current_user.coins < style.cost:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail="哇图币不足")

    # 扣除费用（异步）
    current_user.coins -= style.cost

    # 生成最终提示词
    if style.template_type == TemplateType.VARIABLE_PROMPT:
        if prompt is not None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="VARIABLE_PROMPT 风格请仅提交 variables，不允许直接提交 prompt")
        base_prompt = process_variable_prompt(style, variables)
    else:
        base_prompt = prompt if prompt is not None else style.prompt_template

    # 添加ratio信息到提示词中
    # 检查prompt中是否已包含ratio参数，避免重复添加
    has_ratio = '"ratio":' in base_prompt or 'ratio' in base_prompt.lower()
    if not has_ratio and ratio:
        base_prompt = f"{base_prompt}\n\"ratio\": \"{ratio}\""

    if supports_n:
        final_prompt = f"{base_prompt}\n\"n\": {n_val}"
    else:
        final_prompt = base_prompt

    generation = Generation(
        task_id=str(uuid.uuid4()),
        user_id=current_user.id,
        style_id=style_id,
        prompt=final_prompt,
        images_count=n_val,
        status=GenerationStatus.PENDING,
        cost=style.cost,
        created_at=datetime.utcnow()
    )
    db.add(generation)
    db.commit()
    db.refresh(generation)

    # 写入扣费流水（异步任务）
    db.add(
        CoinTransaction(
            user_id=current_user.id,
            change=-style.cost,
            balance=current_user.coins,
            source=CoinTransactionSource.GENERATE,
            source_id=generation.id,
            remark="生成图片扣费",
        )
    )
    db.commit()

    # 保存源图片列表
    for url in source_image_urls:
        db.add(SourceImage(generation_id=generation.id, image_url=url))
    db.commit()

    # 触发异步生成（不等待结果）
    service = GenerationService(db)
    service.generate_image_background(generation)

    return {
        "task_id": generation.task_id,
        "generation_id": generation.id,
        "status": "PENDING",
        "expected_count": n_val
    }

@router.get("/status/{task_id}")
async def check_generation_status(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """查询图片生成任务状态"""

    # 查找生成记录
    generation = db.query(Generation).filter(
        Generation.task_id == task_id,
        Generation.user_id == current_user.id
    ).first()

    if not generation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="任务不存在")

    # 轮询计数逻辑
    from services.generation_service import PollCounter
    poll_count = PollCounter.increment_and_get(task_id)

    # 每5次轮询打印一次汇总日志
    if poll_count % 5 == 0:
        logger.info(f"用户ID{current_user.id} - 风格ID{generation.style_id} - 任务ID{task_id} - 轮询{poll_count}次")
    
    response = {
        "task_id": generation.task_id,
        "generation_id": generation.id,
        "status": generation.status.value,
        "created_at": generation.created_at,
        "completed_at": generation.completed_at,
        "images_count": generation.images_count
    }
    
    # 如果生成成功或部分成功，返回生成的图片URL列表
    if generation.status in [GenerationStatus.SUCCESS, GenerationStatus.PARTIAL]:
        # 查询与此生成任务关联的所有图片URL
        images = db.query(GenerationImage).filter(
            GenerationImage.generation_id == generation.id
        ).all()

        # 格式化图片URL
        response["generated_image_urls"] = [
            _ensure_path_format(img.image_url) for img in images
        ]

    # 如果生成失败，返回错误信息
    if generation.status == GenerationStatus.FAILED:
        response["error_message"] = generation.error_message

    # 如果任务已完成（成功、失败或部分成功），清理轮询计数器
    if generation.status in [GenerationStatus.SUCCESS, GenerationStatus.FAILED, GenerationStatus.PARTIAL]:
        PollCounter.reset_count(task_id)

    return response

@router.get("/{generation_id}")
async def get_generation(
    generation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """获取生成记录详情"""
    
    # 查找生成记录
    generation = db.query(Generation).filter(
        Generation.id == generation_id,
        Generation.user_id == current_user.id
    ).first()
    
    if not generation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="记录不存在")
    
    # 查询风格信息
    style = db.query(Style).filter(Style.id == generation.style_id).first()
    
    # 查询与此生成任务关联的所有图片URL
    images = db.query(GenerationImage).filter(
        GenerationImage.generation_id == generation.id
    ).all()
    
    # 格式化图片URL
    image_urls = [_ensure_path_format(img.image_url) for img in images]
    
    return {
        "id": generation.id,
        "task_id": generation.task_id,
        "user_id": generation.user_id,
        "style_id": generation.style_id,
        "style_name": style.name if style else None,
        "source_image_urls": [img.image_url for img in generation.source_images],
        "generated_image_urls": image_urls,
        "images_count": generation.images_count,
        "prompt": generation.prompt,
        "status": generation.status.value,
        "created_at": generation.created_at,
        "completed_at": generation.completed_at
    }

@router.get("/")
async def get_user_generations(
    page: int = 1,
    page_size: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """获取当前用户的生成历史"""
    # 计算偏移量
    skip = (page - 1) * page_size
    
    # 查询总数
    total = db.query(Generation).filter(
        Generation.user_id == current_user.id
    ).count()
    
    # 查询数据
    generations = db.query(Generation).filter(
        Generation.user_id == current_user.id
    ).order_by(Generation.created_at.desc())\
     .offset(skip)\
     .limit(page_size)\
     .all()
    
    # 构建响应
    result = []
    for gen in generations:
        style = db.query(Style).filter(Style.id == gen.style_id).first()
        
        # 获取首张预览图（或所有图片）
        preview_images = db.query(GenerationImage).filter(
            GenerationImage.generation_id == gen.id
        ).order_by(GenerationImage.created_at).limit(1).all()
        
        # 预览图URL
        preview_url = None
        if preview_images:
            preview_url = _ensure_path_format(preview_images[0].image_url)
        
        result.append({
            "id": gen.id,
            "task_id": gen.task_id,
            "style_id": gen.style_id,
            "style_name": style.name if style else None,
            "preview_image_url": preview_url,
            "source_image_urls": [img.image_url for img in gen.source_images],
            "images_count": gen.images_count,
            "status": gen.status.value
            ,"created_at": gen.created_at
        })
    
    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "items": result
    }

@router.post("/{style_id}/prefill", summary="获取风格的预填充提示词")
async def get_prefill_clause(
    style_id: int, 
    image_count: int = Body(..., embed=True, description="用户当前上传的图片数量"),
    db: Session = Depends(get_db)
):
    """
    根据风格ID和用户上传的图片数量，获取动态预填充的提示词。
    这个接口非常轻量，仅用于提升前端交互体验。
    """
    style = db.query(Style).filter(Style.id == style_id).first()
    if not style:
        raise HTTPException(status_code=404, detail="风格不存在")

    # 仅对 VARIABLE_PROMPT 类型的风格提供预填充服务
    if style.template_type != TemplateType.VARIABLE_PROMPT:
        return {"prefill": ""}

    try:
        # 使用工厂模式获取风格的具体实现
        style_impl = StyleFactory.get_style_implementation(style.model_identifier)
        
        # 检查该风格实现是否有 get_prefill_clause 方法
        if hasattr(style_impl, 'get_prefill_clause') and callable(style_impl.get_prefill_clause):
            prefill_text = style_impl.get_prefill_clause(image_count)
            return {"prefill": prefill_text}
        else:
            # 如果风格没有定义预填充规则，则返回空
            return {"prefill": ""}
            
    except Exception as e:
        logger.error(f"获取预填充提示词失败: {e}")
        # 即使失败，也返回空字符串，不阻塞前端
        return {"prefill": ""}